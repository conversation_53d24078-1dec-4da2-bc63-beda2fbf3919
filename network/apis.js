import request from '@/network/requests.js'

function get(url, data, json) {
	return request.get(url, {
		params: data
	}, json)
}

function post(url, data, json) {
	return request.post(url, data, json)
}

function put(url, data, json) {
	return request.put(url, data, json)
}

function del(url, data, json) {
	return request.delete(url, data, json)
}

let apis = {
	// 登录接口
	wxLogin: (data) => post('admin-api/system/auth/wxlogin', data, false, true),

	// 下一题
	commitQuestion: (data) => post('admin-api/system/userAnswer/commitQuestion', data, false, true),


	// 所有问题接口
	getqList: (data) => get('admin-api/system/question/restartAnswer', data, false, true),

	// 版本列表
	getWishIntroduce: (data) => get('admin-api/version/wish-version/getWishIntroduce', data, false, true),


	// 生成报告
	generatereport: (data) => post('admin-api/system/report/generate', data, false, true),


	// 报告查询
	getReport: (data) => post('admin-api/system/report/getReport', data, false, true),


	// 查询报告
	getReports: (data) => get('admin-api/system/report/get', data, false, true),


	// 获取专业信息数据
	getmajors: (data) => post('admin-api/system/report/getReport', data, false, true),


	// 获取专业信息数据
	getmajordata: (data) => get('admin-api/system/metadata/ceemajor/recommended-tree', data, false, true),

	getmajordata1: (data) => get('admin-api/system/metadata/ceemajor/tree', data, false, true),


	// 获取问题答案接口
	getRecordAnswer: (data) => get('admin-api/record/answer-record/getRecordAnswer', data, false, true),


	// 查看答案记录接口
	getUserAnswerRecord: (data) => get('admin-api/record/answer-record/getUserAnswerRecord', data, false, true),

	getInfo: (data) => get('admin-api/system/question/get', data, false, true),

	// 获取聊天记录
	conversationList: (data) => get('admin-api/system/ai/chat-conversation/page', data, false, true),

	// 发送聊天消息
	conversationAdd: (data) => post('admin-api/system/ai/chat-conversation/create', data, false, true),

	// 首页
	indexpage: (data) => get('admin-api/system/ai/normal-question/page', data, false, true),

	// 首页问题内容
	indexquestionContent: (data) => get('admin-api/system/ai/normal-question-content/page', data, false, true),

	//获取用户信息
	getUserInfo: (data) => get('admin-api/system/user/get',data,false,true),


	// 根据id获取报告详情
	getReportByid: (data) => get('admin-api/system/report/get', data, false, true),

	// 完善用户学习信息
	updateUserInfo: (data) => put('admin-api/system/user/updateUser', data, false, true),

	// 付费权益
	qygetByUserId: (data) => get('admin-api/system/ai/user-assets/getByUserId', data, false, true),

	// 权益兑换码兑换
	redeemCode: (data) => post('admin-api/system/ai/user-assets-code/redeem', data, false, true),

	// 专业的详细介绍
	getmajorsDetail: (data) => get('admin-api/system/majors/data/detail', data, false, true),

	// 获取专业元数据详情
	getCeeMajorDetail: (data) => get('admin-api/system/metadata/ceemajor/get', data, false, true),

	// 就业方向列表
	getemploymentdir: (data) => get('admin-api/kf/employment-dir/page', data, false, true),

	// 就业方向详情（获取单个行业的详细信息，包括对口院校）
	getemploymentdirGet: (data) => get('admin-api/kf/employment-dir/get', data, false, true),

	// 就业文章详情
	getemploymentDetail: (data) => get('admin-api/kf/employment-essay/page', data, false, true),

	// 省份列表
	getdictData: (data) => get('admin-api/system/dict-data/page', data, false, true),

	// 按省份查看院校
	getschoolList: (data) => get('admin-api/kf/school/page', data, false, true),

	// 创建订单
	createOrder: (data) => post('app-api/trade/order/create', data, false, true),

	// 支付订单
	submitOrder: (data) => post('app-api/pay/order/submit', data, false, true),


	// 订单列表
	getOrder: (data) => get('app-api/trade/order/page', data, false, true)




}
export default apis;