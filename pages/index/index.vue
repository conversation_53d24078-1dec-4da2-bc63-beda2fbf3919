<template>
	<view>
		<view v-if="errorNetwork">
			<view class="error-network">
				<view class="title">网络连接断开</view>
				<view class="con">
					<view class="label">请检查情况：</view>
					<view class="item">· 在设置中是否已开启网络权限</view>
					<view class="item">· 当前是否处于弱网环境</view>
					<view class="item">· 版本是否过低，升级试试吧</view>
				</view>
				<view class="btn" @tap="reconnect">重新连接</view>
			</view>
		</view>
		<view class="indexpage" v-if="!loading&&!errorNetwork">
			<view class="banner">
				<image :src="imgUrl+'/qdkbm/newimage/fhui/banners.png'" mode="widthFix"></image>
			</view>
			<view class="index-content">
				<view class="index-menuList">
					<view class="menu-row first-row">
						<view class="item" @tap="toUrl('/pages/trend/index')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/home-icon1.png'"></image>
							</view>
							<view class="text">行业趋势</view>
						</view>
						<view class="item" @tap="toUrl('/pages/qa/index')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/home-icon2.png'"></image>
							</view>
							<view class="text">志愿100问</view>
						</view>
						<view class="item" @tap="toUrl('/pages/report/index')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/home-icon3.png'"></image>
							</view>
							<view class="text">规划案例</view>
						</view>
						<view class="item" @tap="toUrl('/pages/plan/major')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/home-icon4.png'"></image>
							</view>
							<view class="text">专业解析</view>
						</view>
					</view>
					<view class="menu-row second-row">
						<view class="item" @tap="toUrl('/pages/school/index')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/大学.png'"></image>
							</view>
							<view class="text">查大学</view>
						</view>
						<view class="item" @tap="toUrl('/pages/school/plan')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/招生计划.png'"></image>
							</view>
							<view class="text">招生计划</view>
						</view>
						<view class="item" @tap="toUrl('/pages/score/distribution')">
							<view class="icon">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/一分一段表.png'"></image>
							</view>
							<view class="text">一分一段</view>
						</view>
					</view>
				</view>
				<view class="banner2">
					<view class="banner2-con">
						<view class="btns">
							<image :src="imgUrl + '/qdkbm/newimage/fhui/menu-bg.png'"></image>
							<view class="text">智能问答</view>
						</view>
						<view class="pic">
							<image :src="imgUrl + '/qdkbm/newimage/fhui/logos.png'"></image>
						</view>
						<view class="desc">自由提问高考志愿相关问题</view>
						<view class="sub-desc">AI助手为您解答志愿填报困惑</view>
						<view class="btn2"><button @tap="goAsk">开始提问</button></view>
					</view>
				</view>
				<view class="hot-major">
					<view class="hd">
						<view class="left">
							<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-hot.png'"></image>热门专业
							<view class="tag-hot">HOT</view>
						</view>
						<view class="right" @tap="toUrl('/pages/major/hot')">
							更多<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-rights.png'"></image>
						</view>
					</view>
					<view class="section-desc">了解当前最热门的专业选择</view>
					<view class="major-list">
						<view class="item" @tap="goDetail(item)" :class="'tag'+Number(index+1)"
							v-for="(item,index) in hotmajorList" :key="index">

							<block v-if="index == 0">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-xing.png'"></image>
							</block>
							<block v-if="index == 1">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-xing2.png'"></image>
							</block>
							<block v-if="index == 8">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-xing3.png'"></image>
							</block>

							{{item.question}}
						</view>

					</view>
				</view>
				<view class="notice-major">
					<view class="hd">
						<view class="left">
							慎选专业
							<view class="tag-notice">慎选</view>
						</view>
						<view class="right" @tap="toUrl('/pages/major/careful')">
							更多<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-rights.png'"></image>
						</view>
					</view>
					<view class="section-desc">选择合适的专业，避免踏入误区</view>
					<view class="sx-list">
						<view class="item" @tap="goDetail2(item)" :class="'sx-tag'+Number(index+1)" v-for="(item,index) in sxmajorList" :key="index">
							<block v-if="index == 0">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-xing3.png'"></image>
							</block>
							<block v-if="index == 1">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-xing.png'"></image>
							</block>
							<block v-if="index == 3">
								<image :src="imgUrl + '/qdkbm/newimage/fhui/icon-xing2.png'"></image>
							</block>
							{{item.question}}
						</view>
					</view>
				</view>
				<view class="index-footer">
					<view class="footer-text">升学规划助手 - 您的高考志愿填报专家</view>
				</view>
			</view>
			<myDialog :showbox="showbox" @closebox="closebox" @openVip="openVip"></myDialog>
			<tabBar :current-page="1"></tabBar>
		</view>
		<view v-if="loading&&!errorNetwork">
			<view class="loadBox">
				<view>
					<view class="loadingBox" style="width: 100%; height: 100%">
						<view>
							<view></view>
							<view class="loadingBox-left"></view>
							<view class="loadingBox-right"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import tabBar from '@/components/tabBar.vue'
	import myDialog from '@/components/dialog.vue'
	export default {
		components: {
			tabBar,
			myDialog
		},
		data() {
			return {
				errorNetwork: false,
				showbox: false,
				viewCount: 0,
				hotmajorList: [],
				sxmajorList: [],
				loading: true,
				imgUrl: this.$base.uploadImgUrl,
				titleTop: 0
			}
		},
		onLoad() {
			this.snycNetWork()
			setTimeout(() => {
				this.loading = false
			}, 500)
		},
		onShow() {
			this.getIndexPage()
		},
		mounted() {},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		methods: {

			openVip() {
				uni.navigateTo({
					url: '/pages/vip/index'
				});
				this.showbox = false
			},
			closebox(val) {
				this.showbox = val
			},
			openbox() {
				this.showbox = true
			},
			snycNetWork() {
				uni.getNetworkType({
					success: res => {
						console.log(res)
						//res.networkType === 'none'无网络
						this.errorNetwork = res.networkType === 'none';
						//如果没有网络，清除骨架屏

					}
				});
			},
			// 断网后重新链接
			reconnect() {
				uni.getNetworkType({
					success: res => {
						console.log(res)
						this.errorNetwork = res.networkType === 'none';
						if (!this.errorNetwork) {
							setTimeout(() => {
								uni.reLaunch({
									url: "/pages/index/index"
								});
							}, 1000)
						}
					}
				});
			},
			async goDetail(item) {
				let token = uni.getStorageSync('token')
				let userId = uni.getStorageSync('userId')
				if (token && userId) {
					let res = await this.$store.dispatch('getuseInfo')
					let isVip = uni.getStorageSync('isVip') || false;
					this.viewCount = uni.getStorageSync('tys') || 0
					if (isVip) {
						uni.navigateTo({
							url: "/pages/major/detail?id=" + item.id + '&name=' + item.question
						})
					} else {
						if (this.viewCount <= 0) {
							this.openbox()
						} else {
							this.viewCount--
							uni.setStorageSync('tys', this.viewCount)
							uni.navigateTo({
								url: "/pages/major/detail?id=" + item.id + '&name=' + item.question
							})
						}
					}
				} else {
					// 尝试自动登录
					let loginRes = await this.$store.dispatch('gologinPage')
					if (loginRes) {
						// 自动登录成功，重新调用本方法
						this.goDetail(item)
					} else {
						// 自动登录失败，不做任何处理
						// 用户可以继续使用应用，但无法查看详情
					}
				}
			},
			goDetail2(item) {

				// let token = uni.getStorageSync('token')
				// if (token) {
				// let res = await this.$store.dispatch('getuseInfo')
				// const isVip = uni.getStorageSync('isVip') || false;
				// this.viewCount = uni.getStorageSync('tys') || 0
				// if (isVip) {
				uni.navigateTo({
					url: "/pages/major/sxdetail?id=" + item.id + '&name=' + item.question
				})
				// } else {
				// 	if (this.viewCount <= 0) {
				// 		this.openbox()
				// 	} else {
				// 		this.viewCount--
				// 		uni.setStorageSync('tys', this.viewCount)
				// 		uni.navigateTo({
				// 			url: "/pages/major/sxdetail?id=" + item.id + '&name=' + item.question
				// 		})
				// 	}
				// }
				// } else {
				// 	uni.showModal({
				// 		title: '提示',
				// 		content: '您还未登录，请先登录',
				// 		confirmText: '去登录',
				// 		success: (res) => {
				// 			if (res.confirm) {
				// 				uni.navigateTo({
				// 					url: '/pages/login/login'
				// 				});
				// 			}
				// 		}
				// 	});
				// }

			},
			getIndexPage() {
				this.$apis.indexpage({
					parentType: 3,
					pageNo: 1,
					pageSize: 10
				}).then((res) => {
					if (res.code == 0) {
						this.hotmajorList = res.data.list
					}
				})
				this.$apis.indexpage({
					parentType: 4,
					pageNo: 1,
					pageSize: 10
				}).then((res) => {
					if (res.code == 0) {
						this.sxmajorList = res.data.list
					}
				})
			},
			toUrl(path) {
				// 检查是否是标签栏页面
				if (path === '/pages/index/index' ||
					path === '/pages/plan/plan' || path === '/pages/goldBowl/index' ||
					path === '/pages/center/index') {
					uni.switchTab({
						url: path
					})
				} else {
					uni.navigateTo({
						url: path
					})
				}
			},
			async goAsk() {
				let token = uni.getStorageSync('token')
				if (token) {
					uni.navigateTo({
						url: "/pages_chat/chat/chat"
					})
				} else {
					// 尝试自动登录
					let loginRes = await this.$store.dispatch('gologinPage')
					if (loginRes) {
						// 自动登录成功，跳转到聊天页面
						uni.navigateTo({
							url: "/pages_chat/chat/chat"
						})
					} else {
						// 自动登录失败，不做任何处理
						// 用户可以继续使用应用，但无法进入聊天页面
					}
				}
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.error-network {
		position: fixed;
		left: 0;
		top: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		height: 100%;
		padding-top: 40rpx;
		background: #fff;
		padding-top: 30%;

		.img {
			width: 414rpx;
			height: 336rpx;
		}

		.title {
			position: relative;
			top: -40rpx;
			font-size: 32rpx;
			color: #666;
		}

		.con {
			font-size: 24rpx;
			color: #999;

			.label {
				margin-bottom: 20rpx;
			}

			.item {
				margin-bottom: 20rpx;
			}
		}

		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 508rpx;
			height: 86rpx;
			margin-top: 100rpx;
			border: 1px solid #d74432;
			color: #e93323;
			font-size: 30rpx;
			border-radius: 120rpx;
		}
	}

	.loadBox {
		height: 100vh;
		width: 100vw;
		position: fixed;
		left: 0;
		top: 0;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.48);
		display: -webkit-flex;
		display: flex;
		justify-content: center;
		z-index: 8;
	}

	@keyframes loading {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(1turn);
		}
	}

	@-webkit-keyframes loading {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(1turn);
		}
	}

	.loadingBox {
		position: relative;
	}

	.loadingBox .loadingBox-left {
		height: 60px;
		width: 60px;
		left: 70px;
		position: absolute;
		top: 70px;
		background: #f5f5f5;
		border-radius: 50%;
		box-shadow: 4rpx 4rpx 10rpx rgba(0, 0, 0, 0.08);
	}

	.loadingBox .loadingBox-right {
		width: 18px;
		height: 18px;
		color: #FF8918;
		font-size: 5px;
		left: 91px;
		line-height: 18px;
		animation: loading 0.8s linear infinite;
		background: #FF8918;
		border-radius: 50%;
		position: absolute;
		top: 74px;
		transform-origin: 9px 25px;
	}

	.loadingBox {
		width: 200px !important;
		height: 200px !important;
		transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
	}

	.indexpage {
		padding-bottom: 150rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 150rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 150rpx);

		.banner {
			position: relative;

			image {
				width: 100%;
			}

		}

		.index-content {
			background: #fff;
			border-radius: 56rpx 56rpx 0 0;
			margin-top: -180rpx;
			position: relative;

			.banner2 {
				margin: 0 40rpx;
				border-radius: 24rpx;
				background: #fff;
				box-shadow: 0 4rpx 4rpx 5rpx rgba(0, 0, 0, 0.07);
				padding: 6rpx;

				.banner2-con {
					height: 320rpx;
					border-radius: 24rpx;
					background-image: linear-gradient(180deg, #FFE4C0 0, #FFFBF3 100%);
					position: relative;

					.btns {
						position: absolute;
						left: 40rpx;
						top: 40rpx;

						image {
							width: 140rpx;
							height: 52rpx;
						}

						.text {
							position: absolute;
							left: 0;
							top: 0;
							width: 100%;
							height: 52rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							color: #fff;
							font-size: 28rpx;
						}
					}

					.btn2 {
						position: absolute;
						left: 40rpx;
						right: 40rpx;
						bottom: 40rpx;

						button {
							font-weight: 700;
							letter-spacing: 5rpx;
							height: 80rpx;
							border-radius: 80rpx;
							line-height: 80rpx;
							color: #fff;
							font-size: 32rpx;
							background-image: linear-gradient(180deg, #FF8510 0, #FFBD73 100%);

							&::after {
								border: none;
							}
						}
					}

					.pic {
						position: absolute;
						top: 40rpx;
						right: 60rpx;

						image {
							width: 170rpx;
							height: 240rpx;
						}
					}

					.desc {
						position: absolute;
						left: 40rpx;
						top: 110rpx;
						font-size: 32rpx;
						font-weight: 700;
						color: #333;
					}

					.sub-desc {
						position: absolute;
						left: 40rpx;
						top: 150rpx;
						font-size: 24rpx;
						color: #666;
					}

				}

			}

			.notice-major {
				margin: 0 40rpx;

				.section-desc {
					font-size: 26rpx;
					color: #666;
					margin-bottom: 20rpx;
					padding-left: 10rpx;
				}

				.sx-list {
					display: flex;
					flex-direction: row;
					flex-wrap: wrap;

					.item {
						margin-right: 20rpx;
						margin-bottom: 20rpx;
						height: 52rpx;
						background: #fff;
						border: 2rpx solid #cecece;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #595959;
						font-size: 26rpx;
						padding: 0 20rpx;
						border-radius: 40rpx;
						transition: all 0.3s;

						&:active {
							transform: scale(0.95);
							background: #fff3e7;
							border-color: #ff8918;
							color: #ff8918;
						}

						image {
							width: 32rpx;
							height: 32rpx;
							margin-right: 10rpx;
						}

						&.sx-tag1 {
							background: #fff4f2;
							border-color: #ff583f;
							color: #ff583f;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 88, 63, 0.3);
							}
						}

						&.sx-tag2 {
							background: #f2fff9;
							border-color: #41d996;
							color: #41d996;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(65, 217, 150, 0.3);
							}
						}

						&.sx-tag3 {
							background: #f7fcff;
							border-color: #219eff;
							color: #219eff;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(33, 158, 255, 0.3);
							}
						}

						&.sx-tag4 {
							background: #e9e7ff;
							border-color: #5242ff;
							color: #5242ff;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(82, 66, 255, 0.3);
							}
						}

						&.sx-tag5 {
							background: #fff3e7;
							border-color: #ff8918;
							color: #ff8918;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 137, 24, 0.3);
							}
						}

						&.sx-tag6 {
							background: #ffebf6;
							border-color: #ff5bb4;
							color: #ff5bb4;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 91, 180, 0.3);
							}
						}
					}
				}

				.hd {
					align-items: center;
					padding: 40rpx 0;
					display: flex;
					flex-direction: row;
					justify-content: space-between;

					.left {
						display: flex;
						flex-direction: row;
						color: #2D251E;
						font-size: 32rpx;
						font-weight: 700;

						image {
							width: 40rpx;
							height: 40rpx;
							margin-right: 15rpx;
						}

						.tag-notice {
							background: #FF5B03;
							color: #fff;
							padding: 0 10rpx;
							border-radius: 10rpx;
							margin-left: 10rpx;
							font-size: 24rpx;
							text-align: center;
							line-height: 1.2;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}

					.right {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						color: #999;

						image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}
			}

			.hot-major {
				margin: 0 40rpx;

				.major-list {
					display: flex;
					flex-direction: row;
					flex-wrap: wrap;

					.item {
						margin-right: 20rpx;
						margin-bottom: 20rpx;
						height: 52rpx;
						background: #fff3e7;
						border: 2rpx solid #ff8918;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #FF8918;
						font-size: 26rpx;
						padding: 0 20rpx;
						border-radius: 40rpx;
						transition: all 0.3s;

						&:active {
							transform: scale(0.95);
							box-shadow: 0 2rpx 8rpx rgba(255, 137, 24, 0.3);
						}

						image {
							width: 32rpx;
							height: 32rpx;
							margin-right: 10rpx;
						}

						&.tag2 {
							background: #f7fcff;
							border-color: #219eff;
							color: #219eff;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(33, 158, 255, 0.3);
							}
						}

						&.tag3 {
							background: #fff4f2;
							border-color: #ff583f;
							color: #ff583f;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 88, 63, 0.3);
							}
						}

						&.tag4 {
							background: #f2fff9;
							border-color: #41d996;
							color: #41d996;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(65, 217, 150, 0.3);
							}
						}

						&.tag5 {
							background: #fff3e7;
							border-color: #ff8918;
							color: #ff8918;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 137, 24, 0.3);
							}
						}

						&.tag6 {
							background: #f7fcff;
							border-color: #43abff;
							color: #43abff;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(67, 171, 255, 0.3);
							}
						}

						&.tag7 {
							background: #e9e7ff;
							border-color: #5242ff;
							color: #5242ff;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(82, 66, 255, 0.3);
							}
						}

						&.tag8 {
							background: #fff3e7;
							border-color: #ff8918;
							color: #ff8918;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 137, 24, 0.3);
							}
						}

						&.tag9 {
							background: #fff4f2;
							border-color: #ff573e;
							color: #ff573e;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(255, 87, 62, 0.3);
							}
						}

						&.tag10 {
							background: #f2fff9;
							border-color: #41d996;
							color: #41d996;

							&:active {
								box-shadow: 0 2rpx 8rpx rgba(65, 217, 150, 0.3);
							}
						}
					}
				}

				.hd {
					align-items: center;
					padding: 40rpx 0;
					display: flex;
					flex-direction: row;
					justify-content: space-between;

					.left {
						display: flex;
						flex-direction: row;
						color: #2D251E;
						font-size: 32rpx;
						font-weight: 700;

						image {
							width: 40rpx;
							height: 40rpx;
							margin-right: 15rpx;
						}

						.tag-hot {
							background: #FF8918;
							color: #fff;
							padding: 0 10rpx;
							border-radius: 10rpx;
							margin-left: 10rpx;
						}
					}

					.right {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						color: #999;

						image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}
				.section-desc {
					font-size: 26rpx;
					color: #666;
					margin-bottom: 20rpx;
					padding-left: 10rpx;
				}
			}

			.index-menuList {
				padding: 40rpx 40rpx 0;

				.menu-row {
					display: flex;
					width: 100%;
				}

				.first-row {
					justify-content: space-between;
					margin-bottom: 30rpx;
				}

				.second-row {
					justify-content: flex-start;

					.item {
						margin-right: 60rpx;
					}
				}

				.item {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.icon {
						margin-bottom: 10rpx;

						image {
							width: 88rpx;
							height: 88rpx;
						}
					}

					.text {
						color: #656565;
						font-size: 28rpx;
					}

					.item-desc {
						color: #999;
						font-size: 24rpx;
						margin-top: 10rpx;
					}
				}
			}
		}
		.index-footer {
			background: #fff;
			padding: 20rpx;
			border-top: 1rpx solid #e5e5e5;
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			z-index: 9;

			.footer-text {
				font-size: 24rpx;
				color: #999;
				text-align: center;
			}
		}
	}
</style>