<template>
	<view>
		<view class="bgs">
			<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-bg11.png'" mode="widthFix"
				style="position: absolute;left:0;top:0;width:100%"></image>
		</view>

		<u-popup :round="8" mode="center" closeOnClickOverlay bgColor="transparent" :show="showpopbox"
			@close="closepopbox" @open="openpopbox">
			<view class="picbox"
				style="background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/pop-bg.png');">
				<view class="closeicon" @tap="closepopbox">
					<image src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/fh-cancelbtn.png"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-90rpx;top:-10rpx;z-index:99;">
					<image style="width:220rpx;" mode="widthFix"
						src="https://pic.kefeichangduo.top/qdkbm/newimage/fhpic/popicon1.png"></image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text v-if="confirmText=='微信支付'">您确定花费{{itemInfo.money}}元\n购买<text
								class="bold">{{itemInfo.name}}</text>{{itemInfo.total}}次吗？</text>
						<text v-if="confirmText=='立即使用'">您已购买<text
								class="bold">{{itemInfo.name}}</text>{{itemInfo.total}}次!</text>
					</view>
					<view class="btn"><button :disabled="disabled" @tap="confirmPay">{{confirmText}}</button></view>
				</view>
			</view>

		</u-popup>

		<u-popup :round="8" mode="center" closeOnClickOverlay bgColor="transparent" :show="show" @close="close"
			@open="open">
			<view class="picbox" :style="{'background-image': 'url('+imgUrl+'/qdkbm/newimage/fhpic/pop-bg.png)'}">
				<view class="closeicon" @tap="close">
					<image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-cancelbtn.png'"></image>
				</view>
				<view class="successimg" style="position: absolute;left:50%;margin-left:-70rpx;top:-10rpx;z-index:99;">
					<image style="width:160rpx;" mode="widthFix" :src="imgUrl+'/qdkbm/newimage/fhpic/popicon3.png'">
					</image>
				</view>
				<view class="myboxs">
					<view class="cont">
						<text>消耗购买次数1\n点击开始答题 生成全新报告！</text>

					</view>
					<view class="btn"><button @tap="GoTask">开始答题</button></view>
				</view>
			</view>

		</u-popup>
		<view class="head" style="background:none" :style="'padding-top:' + (titleTop-4) + 'px'">
			<view class="header1">
				<view class="left" @tap="back">
					<view class="itemList">
						<view class="item">
							<button>
								<image style="width:20rpx" :src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
								</image>
							</button>
						</view>
					</view>
				</view>
				<view class="text">{{title?title:'工作台'}}</view>
			</view>
		</view>
		<view class="addpart">
			<button @tap="creatnewReport" class="create-report-btn"
				:style="{'background-image': 'url('+imgUrl+'/qdkbm/newimage/fhpic/fh-tits2.png)','background-color':'transparent','background-repeat':'no-repeat','background-size':'contain','width':'220rpx','height':'80rpx','line-height':'80rpx','font-size':'30rpx','color':'#932804'}">
				<text class="btn-icon">✨</text> 生成新报告</button>
		</view>

		<view class="loading-mask" v-if="loading">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
		<!-- 报告列表 -->
		<view class="report-list">
			<view class="none" v-if="reports.length==0">
				<view class="icon">
					<image :src="imgUrl+'/qdkbm/newimage/nodata-express.png'"></image>
				</view>
				<view class="text">暂无报告数据</view>
				<view class="empty-tip">点击右上角“生成新报告”开始规划之旅</view>
			</view>
			<view class="report-item" v-if="reports.length>0" v-for="(item, index) in reports" :key="index">
				<view class="report-info">
					<view class="report-header">
						<view class="left"
							:style="{'background-image': 'url('+imgUrl+'/qdkbm/newimage/fhpic/fh-tit3.png)'}">
							序号:{{ index + 1 }}
						</view>
						<view class="report-title" v-if="item.reportName">{{item.reportName}}</view>
					</view>
					<view class="right">
						<block v-if="item.status === 1 || item.status === 0">
							<button class="status-btn continue" @click="handleReport(item)">
								<text class="btn-icon">✏️</text> 继续答题
							</button>
						</block>
						<block v-if="item.status === 2">
							<button class="status-btn producing" @click="handleReport(item)">
								<text class="btn-icon">⏳</text> 报告生成中
							</button>
						</block>
						<block v-if="item.status === 3">
							<button class="status-btn view" @click="handleReport(item)">
								查看报告
							</button>
						</block>

					</view>

				</view>
				<view class="report-details" v-if="item.reportCount">
					<view class="report-count">
						<view class="count-label">
							<text class="count-icon">📊</text>
							<text>已生成报告数量</text>
						</view>
						<view class="count-value">{{item.reportCount}}</view>
					</view>
				</view>
				<view class="report-status">
					<text class="status-icon">🕒</text> 开始答题时间： {{formatTime(item.createTime)}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	export default {
		// 启用下拉刷新
		onPullDownRefresh() {
			// 下拉刷新时重新获取数据
			this.refreshData()
		},
		components: {},
		data() {
			return {
				imgUrl: this.$base.uploadImgUrl,
				loading: false,
				myMsg: "",
				title:"",
				openId: "",
				itemInfo: {},
				showpopbox: false,
				confirmText: "",
				show: false,
				ver: "",
				titleTop: 0,
				disabled: false,
				recommend: false, // 标识是否来自首页规划案例
				reports: [
					// {id:1,updateTime:"2025-04-01 15:00",status:1},
					// {id:2,updateTime:"2025-04-01 15:00",status:2},
					// {id:3,updateTime:"2025-04-01 15:00",status:3}
				]
			}
		},
		onLoad(options) {
			if (options.ver) {
				this.ver = options.ver
				if(this.ver == 1){
					this.title = '规划报告-体验版'
				} else{
					this.title = '规划报告-专业版'
				}
			}
			if (Object.keys(options).length > 0) {
				const item = JSON.parse(decodeURIComponent(options.item));
				if (item) {
					// 确保total值符合要求：体验版为1，专业版为3
					if(item.name === '体验版' && item.total !== 1) {
						item.total = 1;
					} else if(item.name === '专业版' && item.total !== 3) {
						item.total = 3;
					}
					this.itemInfo = item
					this.confirmText = "微信支付"
					this.myMsg = '您确定花费9.9元\n购买' + this.itemInfo.name + this.itemInfo.total + '次吗？'
				}
			}
		},
		onReady() {
			let res = uni.getMenuButtonBoundingClientRect();
			this.titleTop = res.top
		},
		onShow() {
			if (uni.getStorageSync('openId')) {
				this.openId = uni.getStorageSync('openId')
			}

			// 检查是否有token，如果有则获取数据
			if (uni.getStorageSync('token')) {
				// 先刷新用户信息，再获取列表数据
				this.$store.dispatch('getuseInfo').then(() => {
					// 获取报告列表
					this.getList()
				}).catch(() => {
					// 即使获取用户信息失败，也尝试获取列表数据
					this.getList()
				})
			} else {
				// 没有token，尝试自动登录
				this.$store.dispatch('gologinPage').then(res => {
					if (res) {
						// 自动登录成功，获取用户信息和列表数据
						this.$store.dispatch('getuseInfo').then(() => {
							this.getList()
						}).catch(() => {
							this.getList()
						})
					} else {
						// 自动登录失败，仍然尝试获取列表数据
						this.getList()
					}
				}).catch(() => {
					// 登录出错，仍然尝试获取列表数据
					this.getList()
				})
			}
		},
		methods: {
			confirmbtn2() {
				let that = this
				that.showpopbox = false
				let version = that.itemInfo.name === '体验版' ? 1 : 2
				setTimeout(() => {
					that.show = true
				}, 200)
			},
			confirmPay() {
				// 如果按钮已经被禁用，直接返回，防止重复点击
				if (this.disabled) {
					return;
				}

				if (this.confirmText == '微信支付') {
					// 设置按钮为禁用状态
					this.disabled = true
					this.$apis.createOrder({
						items: [{
							skuId: this.itemInfo.id == 1 ? 30 : 31,
							count: 1,
							cartId: null
						}],
						couponId: undefined,
						pointStatus: false,
						deliveryType: 2,
						addressId: undefined,
						pickUpStoreId: undefined,
						receiverName: "",
						receiverMobile: "",
						seckillActivityId: undefined,
						combinationActivityId: undefined,
						combinationHeadId: undefined,
						bargainRecordId: undefined,
						pointActivityId: undefined,
						remark: ""
					}).then((res) => {
						if (res.code == 0) {
							this.orderId = res.data.payOrderId
							this.wechatPay()
						} else {
							// 如果创建订单失败，恢复按钮状态
							this.disabled = false
							uni.showToast({
								title: res.msg || '创建订单失败',
								icon: 'none',
								duration: 1500
							})
						}
					}).catch((err) => {
						// 如果发生错误，恢复按钮状态
						this.disabled = false
						uni.showToast({
							title: '网络异常，请重试',
							icon: 'none',
							duration: 1500
						})
					})
				} else {
					this.confirmbtn2()
				}
			},
			wechatPay() {
				this.$apis.submitOrder({
					id: this.orderId,
					channelCode: "wx_pub",
					channelExtras: {
						openid: this.openId
					},
					displayMode: "url",
					returnUrl: ""
				}).then((res) => {
					if (res.code == 0) {
						let obj = JSON.parse(res.data.displayContent)
						this.wepay(obj)
					} else {
						// 如果提交订单失败，恢复按钮状态
						this.disabled = false
						uni.showToast({
							title: res.msg || '支付初始化失败',
							icon: 'none',
							duration: 1500
						})
					}
				}).catch((err) => {
					// 如果发生错误，恢复按钮状态
					this.disabled = false
					uni.showToast({
						title: '网络异常，请重试',
						icon: 'none',
						duration: 1500
					})
				})
			},
			wepay(obj) {
				let that = this
				uni.requestPayment({
					appId: obj.appId,
					timeStamp: obj.timeStamp,
					nonceStr: obj.nonceStr,
					package: obj.packageValue,
					signType: obj.signType,
					paySign: obj.paySign,
					success(res) {
						that.disabled = false
						uni.showToast({
							title: "支付成功",
							icon: "none",
							duration: 1000
						})

						// 直接关闭购买弹窗
						that.showpopbox = false

						// 刷新用户信息
						that.$store.dispatch('getuseInfo').then(userInfo => {
							// 更新剩余次数
							that.itemInfo.count = that.ver == 1 ? userInfo.trailLeftCount : userInfo.psLeftCount

							// 如果购买成功，直接显示答题弹窗
							setTimeout(() => {
								that.show = true
							}, 500)
						})
					},
					fail(res) {
						that.disabled = false
						uni.showToast({
							title: '支付失败',
							icon: 'none',
							duration: 650
						})
					}
				});
			},
			openpopbox() {
				this.showpopbox = true
			},
			closepopbox() {
				this.showpopbox = false
				this.disabled = false
			},
			// 下拉刷新数据
			refreshData() {
				// 调用获取数据方法
				this.getList(true)
			},

			getList(isRefresh = false) {
				// 如果不是下拉刷新，显示加载动画
				if (!isRefresh) {
					this.loading = true
				}

				// 检查是否有版本信息
				if (!this.ver && this.ver !== 0) {
					console.log('版本信息不存在，使用默认值1')
					this.ver = 1 // 默认使用体验版
					this.title = '规划报告-体验版'
				}

				console.log('获取报告列表，版本:', this.ver)

				// 检查是否有token
				if (!uni.getStorageSync('token')) {
					console.log('没有token，无法获取报告列表')
					this.loading = false
					if (isRefresh) {
						uni.stopPullDownRefresh()
					}
					return
				}

				this.$apis.getUserAnswerRecord({
					type: this.ver
				}).then((res) => {
					if (res.code == 0) {
						if(res.data && res.data.length > 0){
							res.data.map((item) => {
								item.updateTime = dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss')
							})
							this.reports = res.data
						} else {
							// 如果没有数据，设置为空数组
							this.reports = []
						}

						// 如果是下拉刷新，停止下拉动画
						if (isRefresh) {
							uni.stopPullDownRefresh()
							uni.showToast({
								title: '刷新成功',
								icon: 'success',
								duration: 1000
							})
						} else {
							// 非下拉刷新时，延时关闭加载动画
							setTimeout(() => {
								this.loading = false
							}, 500)
						}
					} else {
						// 如果返回错误码，清空报告列表
						this.reports = []

						// 如果是下拉刷新，停止下拉动画
						if (isRefresh) {
							uni.stopPullDownRefresh()
							uni.showToast({
								title: '刷新失败',
								icon: 'none',
								duration: 1000
							})
						} else {
							setTimeout(() => {
								this.loading = false
							}, 500)
						}

						// 如果是未登录错误，尝试自动登录
						if (res.msg === '账号未登录') {
							console.log('检测到未登录状态，尝试自动登录')
							this.$store.dispatch('gologinPage').then(loginRes => {
								if (loginRes) {
									// 自动登录成功，重新获取数据
									setTimeout(() => {
										this.getList()
									}, 500)
								}
							})
						}
					}
				}).catch((err) => {
					console.error('获取报告列表失败:', err)
					// 发生错误，清空报告列表
					this.reports = []

					// 如果是下拉刷新，停止下拉动画
					if (isRefresh) {
						uni.stopPullDownRefresh()
						uni.showToast({
							title: '网络异常，请重试',
							icon: 'none',
							duration: 1000
						})
					} else {
						setTimeout(() => {
							this.loading = false
						}, 500)
					}

					// 如果是网络错误，尝试自动登录
					if (err && (err.msg === '账号未登录' || !uni.getStorageSync('token'))) {
						console.log('检测到未登录状态，尝试自动登录')
						this.$store.dispatch('gologinPage').then(loginRes => {
							if (loginRes) {
								// 自动登录成功，重新获取数据
								setTimeout(() => {
									this.getList()
								}, 500)
							}
						})
					}
				})
			},
			async creatnewReport() {
				// 先获取用户信息
				let res = await this.$store.dispatch('getuseInfo')
				// console.log('用户信息:', res)
				if(res){
					this.itemInfo.count = this.ver == 1 ? res.trailLeftCount : res.psLeftCount
					let tycount = 0, zycount = 0;

					// 修复体验版次数判断逻辑
					if(res.trailCount === 0 || res.trailCount === undefined || res.trailCount === null){
						tycount = 1
					} else{
						// 确保体验版次数始终为1
						tycount = 1
					}

					// 修复专业版次数判断逻辑
					if(res.psCount === 0 || res.psCount === undefined || res.psCount === null){
						zycount = 3
					} else{
						// 确保专业版次数始终为3
						zycount = 3
					}

					// console.log('体验版次数:', tycount, '专业版次数:', zycount)

					// 设置购买的总次数
					this.itemInfo.total = this.ver == 1 ? tycount : zycount

					// 根据剩余次数决定显示哪个弹窗
					if (this.itemInfo.count <= 0) {
						this.showpopbox = true
					} else {
						this.show = true
					}
				}
			},
			close() {
				this.show = false
			},
			open() {

				if (this.itemInfo.count <= 0) {
					this.showpopbox = true
				} else {
					this.show = true
				}
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			// 在data中添加isWxSubscribeHandled属性
			GoTask() {
				// 先关闭弹窗
				this.close()

				// 判断是否是微信环境
				// #ifdef MP-WEIXIN
				let isWxEnv = true;
				// #endif
				// #ifndef MP-WEIXIN
				let isWxEnv = false;
				// #endif

				// 如果不是微信环境，直接跳转
				if (!isWxEnv) {
					uni.navigateTo({
						url: '/pages/plan/questionnaire?ver=' + this.ver
					});
					return;
				}

				// 微信环境下的处理
				try {
					// 检查基础库版本是否支持订阅消息
					const systemInfo = uni.getSystemInfoSync();
					const canUseSubscribe = systemInfo.SDKVersion >= '2.8.2';

					if (!canUseSubscribe) {
						// 基础库版本不支持，直接跳转
						uni.navigateTo({
							url: '/pages/plan/questionnaire?ver=' + this.ver
						});
						return;
					}

					// 调用订阅消息API
					uni.showLoading({
						title: '请稍候...'
					});

					uni.requestSubscribeMessage({
						tmplIds: ['45POPQC4FviMFXR7brQeQ_5pnlj9WqPss4LoisLGYxE'],
						success: (res) => {
							console.log('订阅消息结果:', res);
							// 用户已经做出选择（允许或拒绝）
							setTimeout(() => {
								uni.hideLoading();
								uni.navigateTo({
									url: '/pages/plan/questionnaire?ver=' + this.ver
								});
							}, 300);
						},
						fail: (err) => {
							console.error('订阅消息请求失败:', err);
							// 请求失败
							setTimeout(() => {
								uni.hideLoading();
								uni.navigateTo({
									url: '/pages/plan/questionnaire?ver=' + this.ver
								});
							}, 300);
						}
					});
				} catch (error) {
					console.error('订阅消息处理出错:', error);
					// 出错时直接跳转
					uni.hideLoading();
					uni.navigateTo({
						url: '/pages/plan/questionnaire?ver=' + this.ver
					});
				}
			},
			getpList(id) {
				this.$apis.getmajors({
					type: "",
					answerRecordId: id,
					userId: uni.getStorageSync('userId')
				}).then((res) => {
					if (res.code == 0) {
						let str = res.data[0].id
						let reportTitle = res.data[0].name || "升学规划报告"
						uni.navigateTo({
							url: '/pages/plan/report?id=' + str + '&title=' + encodeURIComponent(reportTitle)
						})
					}
				})
			},
			handleReport(item) {
				if (item.status === 1 || item.status === 0) {

					uni.navigateTo({
						url: '/pages/plan/questionnaire?ver=' + this.ver + '&answerNo=' + item.answerNo +
							'&workId=' + item.id
					})
				} else if (item.status === 3) {
					if (this.ver == 1) {

						this.getpList(item.id)


					} else {
						uni.navigateTo({
							url: '/pages/plan/reportList?reportId=' + item.id
						})
					}

				} else {
					uni.showToast({
						title: '报告正在生成中，请稍后查看',
						icon: 'none'
					})
				}
			},
      formatTime(time) {
        return dayjs(time).format('YYYY年MM月DD日HH:mm:ss')
      }
		}
	}
</script>

<style>
	page {
		background: #fef1db;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	}
</style>

<style scoped lang="scss">
	@-webkit-keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}

	@keyframes countloading {
		0% {
			transform: rotate(180deg)
		}

		100% {
			transform: rotate(0deg)
		}
	}


	// .progress-active-loading{animation:progress-active 2.4s cubic-bezier(.23,1,.32,1) infinite;background-color:#fff;height:100%;position:absolute;width:100%;z-index:999}
	@-webkit-keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}

	@keyframes progress-active {
		0% {
			opacity: .1;
			transform: translateX(-100%) scaleX(0)
		}

		20% {
			opacity: .5;
			transform: translateX(-100%) scaleX(0)
		}

		100% {
			opacity: 0;
			transform: translateX(0) scaleX(1)
		}
	}


	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			background-color: rgba(255, 255, 255, 0.9);
			border-radius: 16rpx;
			padding: 30rpx 40rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		}

		.loading-spinner {
			width: 70rpx;
			height: 70rpx;
			animation: countloading 1s ease infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-position: 50%;
			background-repeat: no-repeat;
			background-size: contain;
		}

		.loading-text {
			margin-top: 20rpx;
			font-size: 28rpx;
			color: #AA7248;
			font-weight: bold;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.none {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fff;
		border-radius: 16rpx;
		height: calc(100vh - 360rpx);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

		.icon {
			image {
				width: 320rpx;
				height: 320rpx;
			}
		}

		.text {
			margin-top: 30rpx;
			color: #AA7248;
			font-size: 32rpx;
			font-weight: bold;
		}

		.empty-tip {
			margin-top: 20rpx;
			color: #999;
			font-size: 28rpx;
			padding: 0 60rpx;
			text-align: center;
		}
	}

	.closeicon {
		position: absolute;
		right: -25rpx;
		top: 5rpx;

		image {
			width: 64rpx;
			height: 64rpx;
		}
	}

	.picbox {
		background-repeat: no-repeat;
		background-size: contain;
		width: 600rpx;
	}

	.myboxs {
		margin: 100rpx 30rpx 20rpx 30rpx;
		height: 400rpx;
		flex-direction: column;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;

		.cont {
			margin-bottom: 40rpx;

			text {
				font-weight: 700;
				letter-spacing: 2rpx;
				font-size: 32rpx;
				color: #AA7248;
				line-height: 54rpx;

				.bold {
					color: #e6702b;
					margin: 0 4rpx;
				}
			}
		}

		.btn {
			button {
				color: #fff;
				padding: 5rpx 80rpx;
				border-radius: 70rpx;
				font-size: 32rpx;
				background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

				&::after {
					border: none;
				}
			}
		}
	}

	.addpart {
		padding: 20rpx 0;
		margin-right: 30rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-end;

		button {
			padding: 0;
			margin: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.95);
			}

			&::after {
				border: none;
			}

			.btn-icon {
				margin-right: 6rpx;
				font-size: 28rpx;
			}
		}
	}

	.report-details {
		padding: 20rpx 30rpx;
		background-color: rgba(255, 133, 16, 0.05);
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		border-left: 6rpx solid #FF8510;

		.report-count {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.count-label {
				display: flex;
				align-items: center;

				.count-icon {
					width: 32rpx;
					height: 32rpx;
					margin-right: 12rpx;
				}

				text {
					font-size: 28rpx;
					color: #666;
				}
			}

			.count-value {
				font-size: 36rpx;
				font-weight: bold;
				color: #FF8510;
				padding: 4rpx 16rpx;
				background-color: rgba(255, 133, 16, 0.1);
				border-radius: 8rpx;
			}
		}
	}

	.report-status {
		font-size: 28rpx;
		color: #999;
		display: flex;
		align-items: center;
		margin-top: 10rpx;

		.status-icon {
			margin-right: 8rpx;
			font-size: 28rpx;
		}
	}

	.report-list {
		padding: 30rpx 20rpx;
		position: relative;
	}

	.report-item {
		background: #fff;
		border-radius: 24rpx;
		padding: 35rpx 30rpx;
		flex-direction: column;
		margin-bottom: 30rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		border: 1rpx solid rgba(255, 150, 66, 0.1);

		&:active {
			transform: scale(0.98);
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
		}
	}

	.report-info {
		display: flex;
		width: 100%;
		margin-bottom: 30rpx;
		justify-content: space-between;

		.report-header {
			display: flex;
			align-items: center;
			max-width: 70%;

			.report-title {
				font-size: 28rpx;
				font-weight: 600;
				color: #FF8510;
				margin-left: 15rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		align-items: center; // 添加此行使内容垂直居中

		.left {
			background-repeat: no-repeat;
			background-size: contain;
			height: 50rpx;
			width: 140rpx;
			color: #fff;
			padding-left: 15rpx;
			display: flex;
			align-items: center;

		}
	}

	.report-index {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
		margin-right: 20rpx; // 添加右边距，使序号和按钮之间有空隙
	}

	.report-time {
		font-size: 24rpx;
		color: #666;
		margin-left: 20rpx; // 添加左边距，使时间和序号之间有空隙
	}

	.status-btn {
		font-size: 26rpx;
		padding: 0rpx;
		width: 200rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 35rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

		&::after {
			border: none
		}

		&:active {
			transform: scale(0.95);
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
		}

		.btn-icon {
			margin-right: 8rpx;
			font-size: 28rpx;
		}

		&.continue {
			background: #FFF6F2;
			color: #FB6B3D;
			border: 1rpx solid #FB6B3D;
		}

		&.producing {
			background: #fff;
			color: #FB6B3D;
			border: 1rpx solid #FB6B3D;
		}

		&.view {
			background-image: linear-gradient(90deg, #FF9642 0, #FB6E3F 100%);
			color: #fff;
			border: none;
			box-shadow: 0 8rpx 16rpx rgba(251, 110, 63, 0.25);
			font-weight: bold;
			letter-spacing: 2rpx;
		}
	}
</style>